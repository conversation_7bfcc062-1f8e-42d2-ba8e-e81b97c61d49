<div align="center">
  <img alt="Prisma Logo" src="public/prisma_logo_linear.png" width="580" />
</div>
<br />

# PRISMA - Programma Interno per la Scansione dei Manoscritti

**PRISMA** is a specialized desktop application developed for the Vatican Apostolic Library (BAV) to manage and control the digitization process of manuscripts and historical documents. Built with Electron and Svelte, it provides a comprehensive interface for scanner control, image processing, and manuscript cataloging.

<br />
<div align="center">

[![Built with Electron](https://img.shields.io/badge/Built%20with-Electron-47848f.svg)](https://electronjs.org/)
[![Built with Svelte](https://img.shields.io/badge/Built%20with-Svelte-ff3e00.svg)](https://svelte.dev/)
[![Version](https://img.shields.io/badge/Version-1.3.0-blue.svg)](package.json)

</div>

## ✒️ Overview

PRISMA is a professional manuscript digitization application designed specifically for the Vatican Apostolic Library. It provides comprehensive tools for:

- **Scanner Control**: Direct integration with Metis scanning devices for high-quality image capture
- **Image Processing**: Advanced image processing capabilities using Sharp library for TIFF metadata, cropping, and color calibration
- **Manuscript Management**: Complete workflow management for digitization projects with job tracking and nomenclature systems
- **Quality Control**: Built-in validation tools and preview systems for ensuring digitization quality
- **Database Integration**: MySQL integration for storing scan metadata, job information, and user activity logs
- **Authentication**: LDAP integration for secure user authentication within the BAV network

## 🔧 Key Technologies & Dependencies

### Core Framework
- **`electron`** (v30.0.0) - Cross-platform desktop application framework
- **`svelte`** (v3.42.1) - Reactive UI framework for the frontend
- **`svelte-spa-router`** - Client-side routing for the application

### Scanner Integration
- **Metis Scanner Integration** - Custom TCP socket communication for scanner control
- **`sharp`** (v0.31.3) - High-performance image processing for TIFF manipulation and metadata

### Database & Authentication
- **`mysql2`** (v3.1.2) - MySQL database connectivity for job and scan management
- **`ldap-authentication`** (v2.2.9) - LDAP integration for BAV network authentication

### UI & User Experience
- **`svelte-material-ui`** (v6.0.0-beta.15) - Material Design components
- **`svelte-material-icons`** (v2.0.0) - Material Design icons
- **`@zerodevx/svelte-toast`** (v0.9.5) - Toast notifications
- **`d3-selection`** (v3.0.0) - DOM manipulation for image viewers

### Image Viewing & Processing
- **OpenSeaDragon** - Deep zoom image viewer for high-resolution manuscript viewing
- **Custom DZI Generation** - Deep Zoom Images for smooth pan/zoom experience

### Development & Build Tools
- **`electron-builder`** (v24.9.1) - Application packaging and distribution
- **`electron-updater`** (v6.1.4) - Automatic application updates
- **`rollup`** (v2.56.2) - Module bundler for the frontend code
<br />

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v16 or higher)
- **Yarn** package manager (recommended) or npm
- **Access to BAV Network** for LDAP authentication and database connectivity
- **Metis Scanner** (for production use)

### Installation & Development

```bash
# Clone the repository
$ git clone http://git.bav.local:3000/BAV/prisma.git

# Navigate to project directory
$ cd prisma

# Install dependencies
$ yarn install

# Start development environment
$ yarn electron-dev

# Build for production
$ yarn electron-pack
```

### Available Scripts

- `yarn dev` - Start Svelte development server with hot reload
- `yarn build` - Build the Svelte application for production
- `yarn electron-dev` - Start the application in development mode
- `yarn electron-pack` - Package the application for distribution
- `yarn start` - Serve the built application locally

## 🏗️ Architecture

PRISMA follows a modern Electron architecture with secure IPC communication between processes:

### Main Process (`src/main-process/`)
The main process handles:
- **Application lifecycle** and window management
- **IPC handlers** for database, filesystem, authentication, and scanner operations
- **Security** with sandboxed renderer process and controlled API exposure
- **Auto-updates** from the BAV internal update server
- **Protocol handlers** for local file access (`local://` and `isilon://`)

### Renderer Process (`src/renderer-process/`)
The frontend application built with Svelte includes:
- **Pages**: Login, Job Manager, Acquisition interface, Preview Visualizer
- **Components**: Modular UI components for scanner control, image viewing, and data management
- **Store**: Centralized state management using Svelte stores
- **Routing**: SPA routing with `svelte-spa-router`

### Key Features

#### Scanner Integration
- Direct TCP communication with Metis scanning devices
- Real-time scan progress monitoring
- Automatic image processing and metadata embedding
- Color calibration and quality control tools

#### Image Management
- **OpenSeaDragon** integration for high-resolution image viewing
- **DZI (Deep Zoom Images)** generation for smooth pan/zoom
- **Sharp** library for TIFF processing and metadata manipulation
- Automatic cropping and image optimization

#### Database Integration
- MySQL connectivity for job tracking and metadata storage
- User activity logging with scanner and host information
- Job recovery and validation workflows

#### User Interface
- **Material Design** components with Svelte Material UI
- **Responsive layout** optimized for scanning workflows
- **Toast notifications** for user feedback
- **Keyboard shortcuts** for efficient operation

### IPC Communication

The application uses Electron's IPC system for secure communication:

```js
// Renderer to Main Process
const result = await window.electron.database('db-get-jobs', params);
const scanData = await window.electron.metis('msd-scan', scanParams);
const processedImage = await window.electron.sharp('build-dzi', imageParams);
```

### Project Structure
```
src/
├── main-process/           # Electron main process
│   ├── ipc-handlers/      # IPC command handlers
│   ├── utilities/         # Utility functions
│   └── workers/           # Background workers
└── renderer-process/       # Svelte frontend
    ├── components/        # Reusable UI components
    ├── pages/            # Application pages/routes
    ├── utilities/        # Frontend utilities
    └── store.js          # State management
```

## 🔐 Configuration

### Database Configuration
Update `src/main-process/config.js` with your MySQL database settings:

```js
exports.dbOptions = {
  host: "your-database-host",
  port: 3306,
  database: "your-database-name",
  user: "your-username",
  password: "your-password"
}
```

### LDAP Authentication
Configure LDAP settings for BAV network authentication:

```js
exports.ldapOptions = {
  url: 'ldap://your-ldap-server',
  bindDN: 'your-bind-dn',
  bindCredentials: 'your-credentials',
  searchBase: 'your-search-base',
  searchFilter: '(|(sAMAccountName={{username}})(mail={{username}}))'
}
```

## 📦 Building & Distribution

### Development Build
```bash
yarn build
```

### Production Package
```bash
yarn electron-pack
```

The application will be packaged using `electron-builder` with the following features:
- **Auto-updater** integration with BAV internal server
- **NSIS installer** for Windows deployment
- **Code signing** (configure in `package.json` build section)

## 🔄 Version History

- **v1.3.0** - Current stable version
- **v7.0.0-alpha** - Major overhaul with validator functionality
- **v1.0.0-beta** - DZI image generation and improved viewers
- **v1.0.0** - Initial production release

## 🤝 Contributing

This is an internal BAV project. For development:

1. Follow the existing code style and patterns
2. Test thoroughly with actual scanning hardware when possible
3. Update documentation for any new features
4. Ensure compatibility with the BAV network infrastructure

## 📄 License

Internal use only - Vatican Apostolic Library (BAV)

---

**PRISMA** - Enhancing the preservation and accessibility of historical manuscripts through modern digitization technology.